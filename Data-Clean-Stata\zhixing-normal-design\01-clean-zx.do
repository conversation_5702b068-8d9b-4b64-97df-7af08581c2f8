***************************************************
* SCREEN-ON PUSH ANALYSIS I: ZHIXING
* This file: Do-file for first-step data cleaning
* Author: Xuhang Fan
* File Structure:
*   - 01-clean-zx.do: Data import and cleaning
*   - 02-merge-zx.do: Merging datasets
*   - 03-main-rd-zx.do: Main regression discontinuity analysis
*   - 04-extra-rd-zx.do: Additional regression discontinuity analysis
*   - output/: Directory for results and logs
***************************************************
*!version: 2025-05-25, STATA 18
***************************************************
* ssc install gtools
* ssc install ftools
***************************************************

di ">>> RUNNING DATA CLEAN <<<"

****************
* 0. Settings  *
****************
clear all
set seed 123
cd "I:\Dropbox\Research\Screen-on Push\Raw Data\Zhixing"
log using "output/01-clean-zx.log", replace
include "I:\Dropbox\Research\Screen-on Push\Data-Clean-Stata\recode.do"
* set processors 32
* set max_memory 128g

****************
* 1. Data Load *
****************

* Define data list
local data_list1 "zhixing_cid_logincnt_0402_0410.txt zhixing_else_push_times_0402_0410.txt zhixing_liangping_push_time_0402_0410_v3.txt zhixing_liangping_time_0402_0410.txt zhixing_usertags_0402_0410.txt"
local data_list2 "zhixing_cid_logincnt_0411_0417.txt zhixing_else_push_times_0411_0417.txt zhixing_liangping_push_time_0411_0417.txt zhixing_liangping_time_0411_0417.txt zhixing_usertags_0411_0417.txt"
local data_list3 "zhixing_cid_logincnt_0418_0425.txt zhixing_else_push_times_0418_0425.txt zhixing_liangping_push_time_0418_0425.txt zhixing_liangping_time_0418_0425.txt zhixing_usertags_0418_0425.txt"
local data_list4 "zhixing_cid_logincnt_0426_0506.txt zhixing_else_push_times_0426_0506.txt zhixing_liangping_push_time_0426_0506_v3.txt zhixing_liangping_time_0426_0506.txt zhixing_usertags_0426_0506.txt"
local extra_data "zhixing_cid_0823_mid5.txt"
local all_lists "data_list1 data_list2 data_list3 data_list4 extra_data"

* Function: rename_zhixing
* Purpose: Import data (raw .txt) and rename variables
* Arguments:
    * data_name - Name of the raw data file to process
    * i - Batch number for output file naming
* Output: Standardized .dta dataset in current dir
capture program drop rename_zhixing
program define rename_zhixing
    args data_name i
    * Read Data
    import delimited `data_name'
    * Rename variables based on the file name
    if regexm(`"`data_name'"', "usertags") | "`data_name'" == "zhixing_cid_0823_mid5.txt" {
        local var_names "user_id age gender is_parent spending_level job recent_behavior province city phone_brand phone_lang phone_carrier phone_model interest_tags"
        rename (v1-v14) (`var_names')
    }
    else if regexm(`"`data_name'"', "liangping_time") {
        if "`data_name'" == "zhixing_liangping_time_0402_0410.txt" | "`data_name'" == "zhixing_liangping_time_0426_0506.txt" {
            local var_names "user_id screenon_time screenon_appid screenon_appname"
            rename (v1-v4) (`var_names')
            drop v5
        }
		else {
            local var_names "user_id screenon_time screenon_appid screenon_appname"
            rename (v1-v4) (`var_names')
        }
    }
    else if regexm(`"`data_name'"', "liangping_push_time") {
        local var_names "app_id task_id user_id ddl_time request_time sent_time arrival_time click_time sent_success"
        rename (v1-v9) (`var_names')
    }
    else if regexm(`"`data_name'"', "cid_logincnt") {
        local var_names "day user_id login_count"
        rename (v1-v3) (`var_names')
    }
    else if regexm(`"`data_name'"', "else_push_times") {
        if "`data_name'" == "zhixing_else_push_times_0402_0410.txt" | "`data_name'" == "zhixing_else_push_times_0426_0506.txt" {
            local var_names "user_id other_push_count day"
            rename (v1-v3) (`var_names')
        }
		else {
            local var_names "day user_id other_push_count"
            rename (v1-v3) (`var_names')
        }
    }
    * Store the data
    if regexm("`data_name'", "usertags") | "`data_name'" == "zhixing_cid_0823_mid5.txt" {
        local output_file "usertags_`i'.dta"
    }
    else if regexm("`data_name'", "liangping_time") {
        local output_file "screenon_df_`i'.dta"
    }
    else if regexm("`data_name'", "liangping_push_time") {
        local output_file "push_df_`i'.dta"
    }
    else if regexm("`data_name'", "cid_logincnt") {
        local output_file "login_count_`i'.dta"
    }
    else if regexm("`data_name'", "else_push_times") {
        local output_file "other_push_count_`i'.dta"
    }
    save "`output_file'", replace
    di "`output_file' stored"
    clear
end

* Call rename_zhixing
local i = 1
foreach list in `all_lists' {
	di "=== Process Batch `i': ==="
	foreach data of local `list' {
		di "=== Processing file: `data' ==="
		rename_zhixing `data' `i'
	}
	local i = `i' + 1
}

* Function: compile_data
* Purpose: Combine multiple batch datasets into single consolidated datasets
* Arguments:
    * x - Starting batch number
    * y - Ending batch number
    * folder_name - Output directory
* Output: Combined .dta files for each dataset type in specified folder
capture program drop compile_data
program define compile_data
    args x y folder_name

    local datasets "push_df usertags screenon_df login_count other_push_count"

    foreach prefix in `datasets' {
        local first_file "`prefix'_`x'.dta"
        di "=== Start using `first_file' ==="
        use "`first_file'", clear

        forval i = `=`x' + 1'/`y' {
            local next_file "`prefix'_`i'.dta"
            di "=== Adding `next_file' ==="

            // Check if file exists
            capture confirm file "`next_file'"
            if !_rc {
                append using "`next_file'"
            }
			else {
                di in red "File `next_file' not found, skipping..."
            }
        }

        // Save final dataset
        local output_file "`folder_name'/`prefix'.dta"
        save "`output_file'", replace
        di "=== Saved: `output_file' ==="
    }
end

* call compile_data
compile_data 1 4 "0402_0506"

****************************
* 2. Data Recode and Clean *
****************************

* Function: clean_zhixing
* Purpose: Clean and standardize all datasets
* Arguments:
    * folder_name - Directory containing the raw datasets to be cleaned
* Output: Cleaned and standardized .dta files with proper date formats and filtered data
capture program drop clean_zhixing
program define clean_zhixing
	args folder_name
	cd "`folder_name'"
	* Data 1. login_count
	* Convert date var in login_count data
	use "login_count.dta", clear
	gen str8 day_str = string(day, "%08.0f")
	gen day_date = date(day_str, "YMD")
	format day_date %td
	drop day day_str
	rename day_date day
	save login_count, replace

	* Data 2: other_push_count
	* Convert date var in other_push_count data
	use "other_push_count.dta", clear
	gen str8 day_str = string(day, "%08.0f")
	gen day_date = date(day_str, "YMD")
	format day_date %td
	drop day day_str
	rename day_date day
	save other_push_count, replace

	* Data 3: usertags
	use "usertags.dta", clear
	recode
	save usertags, replace

	* Data 4: push_df
	* Load Data
	use "push_df.dta", clear
	* Drop dup task_id, keeping only unique task_id values
	di "Duplicate Report of task_id"
	gduplicates report task_id
	gduplicates drop task_id, force
	* Conver Date Vars
	foreach var of varlist *_time {
		* Convert the string datetime to a datetime format
		gen double temp = clock(`var', "YMDhms")
		* Replace the original variable with the formatted datetime
		drop `var'
		rename temp `var'
		format `var' %tc
	}
	* Drop Unsuccessfull Push (when sent_success==0)
	keep if sent_success == 1
	drop sent_success
	* Add day
	gen day = dofc(request_time)
    format day %td
	* Keep (user_id, day) unique
	preserve
        duplicates tag user_id day, gen(dup)
        keep if dup != 0
        keep user_id
        duplicates drop user_id, force
        save users, replace
    restore
    merge m:1 user_id using users
    drop if _merge == 3
    drop _merge
    erase users.dta
	* Drop app_id
	drop app_id
	* Store data
	save push_df, replace

	* Data 5: screenon_df
	use "screenon_df.dta", clear
	* Conver Date Vars
	gen double temp = clock(screenon_time, "YMDhms")
	* Replace the original variable with the formatted datetime
	drop screenon_time
	rename temp screenon_time
	format screenon_time %tc
	* Add day
	gen day = dofc(screenon_time)
	format day %td
	* Drop if screenon_time missing
	drop if missing(screenon_time)
	* Store
	save screenon_df, replace
end

* Call clean_zhixing
clean_zhixing "0402_0506"

di ">>> ENDING DATA CLEAN <<<"

log close _all