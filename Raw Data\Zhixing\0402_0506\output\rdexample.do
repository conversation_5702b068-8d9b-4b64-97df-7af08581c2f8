// directory setup:
local root_dir //input desired directory
cd "`root_dir'"

// packages:

ssc install estout, replace
net install rdrobust, from(https://raw.githubusercontent.com/rdpackages/rdrobust/master/stata) replace

//data:
use "https://raw.githubusercontent.com/rdpackages/rdrobust/master/stata/rdrobust_senate.dta", clear

//Rdrobust Regressions:
eststo class_bwauto: rdrobust class margin, all
eststo class_bw5: rdrobust class margin, all h(5 5)
eststo class_bw10: rdrobust class margin, all h(10 10)

eststo population_bwauto: rdrobust class margin, all
eststo population_bw5: rdrobust class margin, all h(5 5)
eststo population_bw10: rdrobust class margin, all h(10 10)

// Append subsequent results
esttab *_bw5 using "table.tex", append f ///
    b(3) se(3) star(* 0.10 ** 0.05 *** 0.01) ///
    keep(Robust) varlabels(Robust "Bandwidth = 5") ///
    label booktabs nodep nonum nomtitles nolines noobs nonotes collabels(none) ///
    alignment(D{.}{.}{-1})
