Style format for comments in Stata do file:

di "=== RUNNING FILE ==="

******************
* Sample Section *
******************

* In-line Comment
count

* For all commands, clearly specify options although some are default.
* For di command: 
forvalues l = 1/3 {
	winsor2 lc, replace cuts(1 99) trim
	di "=== lc`l' winsorized (replaced at 1%, 99%) ==="
}

di "=== Within-section Comments ==="

*****************
* Stored Results*
*****************

* RD plot
* "rdp_lc`l'_p`p'_bw`bw'_c`c'.pdf"
* lc: which lc
* p: polynomial order
* bw: bandwidth
* c: cutoff


di "=== ENDING FILE ==="
