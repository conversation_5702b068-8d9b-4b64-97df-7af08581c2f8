---
title: "Fuzzy RDD Design of Screen-on Push (Zhixing)"
author:
  - name: <PERSON><PERSON> Fan
    affiliation: Rotman School of Management
    email: <EMAIL>
date: today
date-format: short
format:
  typst:
    eval: true
    toc: true
    code-annotations: false
    mainfont: Times New Roman
    columns: 1
# format:
#   html:
#     output-file: "screen-on-rd"
#     toc: true
#     html-math-method: katex
#     embed-resources: true
#     self-contained-math: true
#     code-tools: true
#     code-fold: true
#     code-overflow: wrap

---

This document presents the RD (Regression Discontinuity) design of evaluating the Screen-on Push policy using the company data from 51job.

### Why RDD

The data perfectly represents a localized random experiment. 

- *Perfect non-compliance*. Participants are randomly assigned to different treatment groups without personal incentives to manipulate the whole process. In other words, we have good knowledge the mechanism of treatment assignment.
- *Clear cutoffs*. The cutoff is clearly defined: the time point that a push request was submitted

The data perfectly accommodates a multi-cutoff and fuzzy RD design. The main features are

- **multi-cutoff**. Each push task is targeted for a single user, thus the absolute time when the treatment happen is heterogenous among participants. 
- **fuzzy design**. Some people _before_ treatment also received the treatment but ALL people after cutoff is in Treatment group.

**Note**. The design mentioned above requires that we only view the screen-on signal _before_ the push notification arrives. However, each individual do have a **distribution** $X_i$ of screen-on signals and seemingly we are specualting about the results based on a random sample from joint distribution $(X_1, X_2, ... X_n)$.  


```{r setup, include=FALSE}
knitr::opts_chunk$set(echo = TRUE)
# R Libs
library(readxl)
library(ggplot2)
library(dplyr)
library(tidyr)
library(lubridate)
library(openxlsx)
library(data.table) # use fread
library(scales)

# Read Stata dta
library(haven)

# Reg
library(AER)
library(locfit)

# Settings
gap = 5
instant_gap = 1
setwd("D:\\Dropbox\\Research\\Screen-on Push\\Raw Data\\zju\\0411_0425")
```


## Data Clean

The data clean part is coded with Stata at the folder `Data-Clean-Stata`. 

1. `1-data-clean.do`
  - rename data 
  - recode data
  - drop data: at `push_df`, drop `(user_id, day)` pair with duplicate(problematic) `task_id`
2. `2-data-gen.do`

The final data output is `merged_df.dta`.

## Data Check

**About Raw Data**. We received 4 types of data in several batches. Their corresponding variables can be found in `Variable Codebook.yaml`. 

- `login_count`
- `else_push_times`
- `screen_on`
- `usertags`

**About Users**. The users for analysis are restricted to people only who have received push requests and produced screen-on signals during the period 04/11/2024 - 04/25/2024.  

Here I go through the following parts:

1. Distribution of Push Tasks
2. Uniqueness of `task_id` and `user_id`. This explores whether the sample person may receive multiple push requests at the same day. 
3. Random arrival of screen-on signals. This proves that even for multiple push tasks, the arrival of screen-on signals in relative format is uniformly distributed *before* and *after* the request, which is a key assumption in our identification strategy.
4. Instant push. A special case of described push mechanism.
5. Bad Push. push with duplicate `task_id`

Note. Our previous check about double push is not a problem now since the `task_id` become unique.
Note. However, `task_id` still has multiple-cases. Reasons researched below. 

### Distribution of Push Tasks

The following figure plots the relative percentages of push requests by day. 

```{r plot_task_dist}
task_dist <- fread("task_dist.csv")
task_dist <- task_dist %>%
  mutate(request_time = dmy_hms(request_time))
task_dist %>%
  # Extract the date part from request_time
  mutate(date = as_date(request_time)) %>%
  # Group by date
  group_by(date) %>%
  # Count the frequency of each date
  summarise(frequency = n()) %>%
  # Calculate percentage
  mutate(percent = frequency / sum(frequency) * 100) %>%
  # Plot the results
  ggplot(aes(x = date, y = percent)) +
  geom_bar(stat = "identity", fill = "skyblue") +  # Use bar plot
  labs(title = "Relative percentage of Push requests by Day",
       x = "Date",
       y = "Percentage (%)") +
  scale_y_continuous(labels = scales::percent_format(scale = 1)) +  # Format y-axis as percentage
  scale_x_date(date_labels = "%m/%d", date_breaks = "1 day") +  # Show date in MM/DD format for each bar
  geom_text(aes(label = sprintf("%.1f%%", percent)), 
            vjust = -0.5, color = "black", size = 3) +  # Show percentage on top of each bar
  theme_minimal() +
  theme(axis.text.x = element_text(angle = 45, hjust = 1))  # Rotate x-axis labels
```

### Multiple Push for Same Person

A potential concern is that multiple push notifications may be sent to the same user, which could lead to complex effects. To investigate this, I calculated the ratio of users who received multiple push notifications in the same day. The results largely depend on the company's push strategy. Fortunately, all instances of multiple pushes for the same person appear to be system retries, which account for only a small percentage of the total and are thus unlikely to have a significant impact. The data can be seen at `tag_multi_push.csv`. 

```{r tag_multi_push}
tag_multi_push <- fread("tag_multi_push.csv")
print(paste0("How many rows: ", nrow(tag_multi_push)))
```

### Random Arrival of Screen-on Signals

This section examines whether the screen-on signals from phone users are randomly distributed relative to the request time of a push task, providing evidence for the natural experiment and our gap-method for causal identification.

**Methods**. Two checks are completed using a pre-determined time gap: 15 minutes.

1. for all users who got pushed, the screen-on signals in general are random   
2. for all tasks, the most recent screen-on signal before `sent_time` is random

**Check 1**: the simple density and QQ plot both indicates that the screen-on signals in general are uniformly distributed in `(-gap, gap)`. 

```{r plot_random_arrival}
# Plot 1: Time Density around Request Time
# Plot 2: Quantiles of screenon_time_relative
plot_screenon_time <- function(df, gap) {
  # Apply Gap
  filtered_df <- df %>%
    filter(screenon_time_relative >= -gap & screenon_time_relative <= gap)
  
  # Plot density of screenon_time_relative
  plot_density <- ggplot(filtered_df, aes(x = screenon_time_relative)) +
    geom_density(fill = "blue", alpha = 0.5) +
    labs(title = "Density of Screen-On Time (Relative to Request Time)",
         x = "Relative Screen-On Time (minutes)",
         y = "Density") +
    theme_minimal()
  
  # Plot quantiles of screenon_time_relative
  plot_quantiles <- ggplot(filtered_df, aes(sample = screenon_time_relative)) +
    stat_qq(distribution = qunif, dparams = c(-gap, gap)) +
    stat_qq_line(distribution = qunif, dparams = c(-gap, gap)) +
    labs(title = "Quantile Plot of Screen-On Time (Relative to Request Time)",
         x = "Theoretical Quantiles (Uniform Distribution)",
         y = "Sample Quantiles") +
    theme_minimal()
  return(list(plot_density, plot_quantiles))
}

# Call the function with your dataframe
random_arrival <- fread("random_arrival_30.csv")
plots <- plot_screenon_time(random_arrival, gap = 10)  # Adjust gap value as needed

# Print each plot separately
print(plots[[1]])
print(plots[[2]])
```

### Instant Push

This section aims to check why many notifications are released instantly after the request. The possible reason is that the server receive a before-request screen-on signal and then recognize the user as an "awake" one. So the next problem is that what might be the reservation time for a before-request screen-on signal. I plotted a density of the time gap between the most recent screen-on signal time and the request time for users with an instant push (i.e. receive push within 30 seconds).

```{r plot_screenon_density}
# Plot
plot_screenon_density <- function(df) {
  # Plot density of screenon_before_request
  ggplot(df, aes(x = screenon_time_relative)) +
    geom_density(fill = "skyblue", color = "blue") +
    labs(
      title = "Density of Latest Screen-on Signal (Relative)",
      x = "Latest Relative Screen-on Signal (minutes)",
      y = "Density"
    ) +
    theme_minimal()
}
# Call
instant_df = fread("instant_df.csv")
plot_screenon_density(instant_df)
```

### Bad Push

- *Bad Push 1: Multi-task-id problem*.  98.12% are unique and 1.71% are 2-dup rows and else are more.

  - I dropped (`user_id`, `day`) pair appearing dup-task-id data due to its limited effect on our results (size = 8.2W of 178.6W, 4.6%).
  - In other words, to drop bad tasks but with limited dropping size, for these people related to multi-task issues, I assume people never receive the push at the `requested_day` but still exist in other days. 
  - For duplicates more than 40 times, it seems that the system will retry push many times. 
  - **Results**. 34.5W of 1272W rows dropped. 

- *Bad Push 2: long lagging after sent*. Most of the push tasks arrived quickly(i.e. within .2 minutes), but a few arrived long after the push was sent. 

  <img src="https://raw.githubusercontent.com/Fanisting/upgit-typora/master/tmpD064.png" alt="tmpD064" style="zoom:33%;" />

  - the figure shows the distribution of `sent_lag = arrival_time - sent_time (mins)` after winsor with 1%. 
  - Ignoring missing value, the 99% of `sent_lag` is 0.8 mins. Similarily, I dropped the `(user_id, request_day)` pair.  
  - **Results**. 26.9W of 1238.4W (after doing Bad Push 1) dropped.


### Distribution: Getting Treated and Screen-on Signal

This section investigates the probability of getting treated before and after the request time.

Data are kept if:\
1. Signals are sent successfully. `sent_success == 1`\
2. For each task, the user must has at least one screen-on signal.

**Define Treatment**. In order to define whether a person should be treated or not, I use the time $t$ in minutes relative to `request_time` per user as notation and listed following cases in order to categorize behaviors. Although users may have multiple screen-on signals, the signal that determines their type is the most recent one relative to `sent_time`. 

1.  Instant Push (`treated = 1`): `sent_time_relative` in \[0, 1\] and `screenon_time_relative <= 1`.
  -   People either got instant push or sent screen-on signals instantly after the request.
2.  Screen-on Push (`treated = 1`): `sent_time_relative` in \(1, 120\] and `screenon_time_relative` in \[1, 120\].
3.  Normal Push (`treated = 0`): `sent_time_relative` in \(120, .) and `screenon_time_relative` in \(., 0\)
4.  Unknown (`treated = -99`)
  - System Error: Screen-on in \[1, 120\] but push after 120 
  - Delayed Push: Both screen-on and push are after 120
  - Other

```{r treat_p}
treat_df <- fread("treat_df_30.csv")
# treat_df <- treat_df %>%
#   filter(screenon_near_int <= gap & screenon_near_int >= -gap)

ggplot(treat_df, aes(x = screenon_near_int, y = treated)) +
  stat_summary(fun = mean, geom = "point", size = 2, color = "red") +
  labs(x = "Screen On Time (Most Recent, in Minutes)", y = "Treated Probability (%)") +
  # scale_x_continuous(breaks = seq(0, 1440, by = 60), labels = seq(0, 24, by = 1)) +
  scale_y_continuous(labels = percent_format(scale = 100)) +  # Convert y-axis to percentage format
  theme_minimal() +
  theme(
    axis.text = element_text(size = 12),  # Adjust text size on axes
    axis.title = element_text(size = 14, face = "bold"),  # Bold axis titles
    plot.title = element_text(size = 16, hjust = 0.5),  # Center plot title
    panel.grid.major = element_blank(),  # Remove major gridlines
    panel.grid.minor = element_blank(),  # Remove minor gridlines
    panel.border = element_blank(),  # Remove plot border
    legend.position = "none"  # Remove legend
  ) +
  geom_vline(aes(xintercept = 0)) +
  geom_vline(aes(xintercept = 120)) + 
  xlim(c(-30, 30))  # Set x-axis limits
```


## Balance Check

This section uses data `treat_df` after filtering into [-`gap`,`gap`] to examine whether there exists significant difference between treatment and control group. The proposed variables for tests are:

1. Demographic variables: `age`, `gender`, `spending_level`, `login_count_before`, `login_count_today`, `login_count_after`, `other_push_count_today`, `east_province`, `phone_model`, `phone_model_above_median`, `is_big_phone_brand` (this data does not contain `interest_tags`)
2. Login Count  
-   `login_count_before`: login number for the current app yesterday


### Regression Tests

The regression model takes `var_for_test` as Y and `after` as X, in which `after` indicates whether the most recent screen-on signal happens before or after the push request. Results will be printed in a table, in which declares the coefficient and p-value.  

```{r reg_test}
# Define the function for Regression Test
reg_test <- function(vars, data, indep_var, gap) {
  # Create a data frame to store the results
  results <- data.frame(Variable = character(), Coefficient = numeric(), P_Value = numeric(), stringsAsFactors = FALSE)
  
  # Subset the data based on the time gap
  subset_data <- data %>%
    filter(screenon_time_relative >= -gap & screenon_time_relative <= gap)
  
  # Winsorization function
  winsorize <- function(x, prob = 0.05) {
    lower <- quantile(x, prob)
    upper <- quantile(x, 1 - prob)
    x[x < lower] <- lower
    x[x > upper] <- upper
    return(x)
  }
  
  # Loop through each variable and run the linear regression
  for (var in vars) {
    # If the variable is "login_count_yd", apply Winsorization at 5%
    if (var == "login_count_yd") {
      subset_data[[var]] <- winsorize(subset_data[[var]], prob = 0.05)
    }

    # Regression formula
    formula <- as.formula(paste(var, "~", indep_var))
    
    # Fit the linear model
    model <- lm(formula, data = subset_data)
    sum_model <- summary(model)
    
    # Extract the coefficient and p-value for the independent variable
    coef <- sum_model$coefficients[indep_var, "Estimate"]
    p_val <- sum_model$coefficients[indep_var, "Pr(>|t|)"]
    
    # Add the results to the data frame
    results <- rbind(results, data.frame(Variable = var, Coefficient = round(coef, 3), P_Value = format.pval(p_val, digits = 3)))
  }
  
  # Print the results
  print(results)
}


# Load Data
test_df <- fread("test_df.csv")
# Call
vars <- c("age", "gender", "spending_level", "login_count_yd", "login_count_td", "login_count_tm", "other_push_count_td",  "east_province", "phone_model")
reg_test(vars, test_df, "after", gap = 5)
vars = c("phone_model_above_median", "is_big_phone_brand")
reg_test(vars, test_df, "after", gap = 5)
```

### Regression Matrix



