***************************************************
* SCREEN-ON PUSH ANALYSIS I: ZHIXING
* This file: Do-file for data merging
* Author: Xuhang Fan
* File Structure:
*   - 01-clean-zx.do: Data import and cleaning
*   - 02-merge-zx.do: Merging datasets
*   - 03-main-rd-zx.do: Main regression discontinuity analysis
*   - 04-extra-rd-zx.do: Additional regression discontinuity analysis
*   - output/: Directory for results and logs
***************************************************
*!version: 2025-05-25, STATA 18
***************************************************
* ssc install winsor2
* ssc install gtools
* ssc install joinby
***************************************************

di ">>> RUNNING DATA MERGE <<<"

****************
* 0. Settings  *
****************
clear all
set seed 123
cd "I:\Dropbox\Research\Screen-on Push\Raw Data\Zhixing\0402_0506"
log using "02-merge-zx.log", replace
* set processors 32
* set max_memory 128g

***************************
* 1. Generate: merged_df  *
***************************

* Function: gen_merge_df
* Purpose: Merge push notification data with screen-on data and filter by time gap
* Arguments:
    * gap - Time window in minutes around push notification request time
* Output: merged_df.dta containing joined push and screen-on data within specified time window
capture program drop gen_merge_df
program define gen_merge_df
	args gap
    * Joinby
    di "=== Start Joinby ==="
		use push_df, clear
		joinby user_id day using screenon_df
    di "=== End Joinby ==="
    * Sort and Re-order
	sort task_id user_id screenon_time
	order task_id user_id request_time screenon_time sent_time ddl_time arrival_time day
	* Add Time relative to Requests
	gen screenon_t = (screenon_time - request_time) / 60000
    gen sent_t = (sent_time - request_time) / 60000
	* Drop Data
	keep if abs(screenon_t) <= `gap'
	* Store
	save merged_df, replace
end

* Function: gen_total_screenon
* Purpose: Calculate total screen-on count per user across all days
* Arguments: None (operates on screenon_df.dta)
* Output: screenon_total.dta containing user_id and total screen-on count
capture program drop gen_total_screenon
program define gen_total_screenon
	use screenon_df, clear
	gduplicates tag user_id, gen(screenon_total)
	* Add +1 since non-dup rows are = 0
	replace screenon_total = screenon_total + 1
	* Ensure user ids unique
	gduplicates drop user_id, force
	keep user_id screenon_total
	* Save data
	save screenon_total, replace
end

* Function: gen_treat_df
* Purpose: Generate final treatment dataset with user characteristics, login counts, and treatment indicators
* Arguments:
    * gap - Time window in minutes (used for output file naming)
* Output: treat_df_[gap].dta containing complete analysis dataset with all merged variables
capture program drop gen_treat_df
program define gen_treat_df
	args gap
	* Load data
	use merged_df, clear

	* Filter Data
	* Keep the signal closest to each request
	gen double abs_t = abs(screenon_t)
	bysort task_id (abs_t): keep if _n == 1
	drop abs_t

	* Treatment Indicators
	gen treated = (sent_time <= ddl_time)
	gen after = (screenon_t>=0)

	* Generate screen_on_near
	gen screenon_int = int(screenon_t)

	* Merge usertags
	fmerge m:1 user_id using usertags, keep(match master) nogen

	* Generate big_phone_brand: most popular Android phone brands
	gen big_phone_brand = inlist(phone_brand, "华为", "小米", "OPPO", "Vivo")

	* Gen lc0: app login count on the day of push
	* Winsorize at (1, 99) percentiles
	di "=== Start merge with login_count ==="
	merge 1:1 user_id day using login_count, keep(match master)
	rename login_count lc0
	replace lc0 = 0 if _merge == 1 // unmatched indicates no screen-on behaviors
	drop _merge
	winsor2 lc0, cuts(1 99) replace
	di "=== Completed merge with login_count ==="

	* Gen other_push_count_td
	di "=== Start merge with other_push_count ==="
	merge 1:1 user_id day using other_push_count, keep(match master) nogen
	di "=== Completed merge with other_push_count ==="

	* Gen Total Screen-on times in whole period
	di "=== Starting merge with screenon_total ==="
	fmerge m:1 user_id using screenon_total, keep(match master) nogen
	di "=== Completed merge with screenon_total ==="

	* Output
	save "treat_df_`gap'.dta", replace
end


* Call Functions
local gap 30
gen_merge_df `gap'
gen_total_screenon
gen_treat_df `gap'

di ">>> ENDING DATA MERGE <<<"
