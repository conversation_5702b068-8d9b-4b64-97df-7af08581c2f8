***************************************************
* SCREEN-ON PUSH ANALYSIS I: ZHIXING
* This file: Do-file for data merging
* Author: Xuhang Fan
* File Structure:
*   - 01-clean-zx.do: Data import and cleaning
*   - 02-merge-zx.do: Merging datasets
*   - 03-main-rd-zx.do: Main regression discontinuity analysis
*   - 04-extra-rd-zx.do: Additional regression discontinuity analysis
*   - output/: Directory for results and logs
***************************************************
*!version: 2025-05-25, STATA 18
***************************************************

di ">>> RUNNING DATA MERGE <<<"

****************
* 0. Settings  *
****************
clear all
set seed 123
cd "I:\Dropbox\Research\Screen-on Push\Raw Data\Zhixing\0402_0506"
* set processors 32
* set max_memory 128g


**************************
** Generate: merged_df  **
**************************

capture program drop gen_merge_df
program define gen_merge_df
	args gap
	di "Running Function: gen_merge_df"
    * Joinby
    di "Start Joinby"
		use push_df, clear
		joinby user_id day using screenon_df
    di "Finish Joinby"
    * Sort and Re-order
	sort task_id user_id screenon_time
	order task_id user_id request_time screenon_time sent_time ddl_time arrival_time day
	* Add Relative Time
	gen screenon_t = (screenon_time - request_time) / 60000
    gen sent_t = (sent_time - request_time) / 60000
	* Keep Data
	keep if screenon_t <= `gap' & screenon_t >= -`gap'
	* Store
	save merged_df, replace
end

* Generate:
capture program drop gen_total_screenon
program define gen_total_screenon
	use screenon_df, clear
	duplicates tag user_id, gen(screenon_total)
	replace screenon_total = screenon_total + 1
	duplicates drop user_id, force
	keep user_id screenon_total
	save screenon_total, replace
end

* Generate: treat_df
capture program drop gen_treat_df
program define gen_treat_df
	args gap
	* Step 1: Load data
	use merged_df, clear
	
	* Drop
	// 	keep if screenon_t <= `gap' & screenon_t >= -`gap'

	* Step 2: Filter Data
	* keep only one signal closest to request_time
	gen double abs_t = abs(screenon_t)
	bysort task_id (abs_t): keep if _n == 1
	drop abs_t
	
	* Treatment Indicator
	gen treated = (sent_time <= ddl_time)
	
	* Step 5: Generate screen_on_near
	gen screenon_near_int = int(screenon_t)

	* Merge usertags
	di "Starting merge with usertags..."
	merge m:1 user_id using usertags
	drop if _merge == 2
	drop _merge
	di "Completed merge with usertags."
	
	* Gen after
	gen after = 1
	replace after = 0 if screenon_t <= 0
	
	* Gen big_phone_brand
	gen big_phone_brand = inlist(phone_brand, "华为", "小米", "OPPO", "Vivo")
	
	* Gen login_count_td: Match login_count data
	di "Starting merge with login_count..."
	merge 1:1 user_id day using login_count
	replace login_count = 0 if _merge == 1 // if not matched, store as 0
	drop if _merge == 2 // drop rows only from using data_name
	drop _merge
	rename login_count login_count_td
	di "Completed merge with login_count."
	
	* Process login count
	* 1. login_count_td_w with [1, 99] winsor
	winsor2 login_count_td
	
	* Gen other_push_count_td
	di "Starting merge with other_push_count..."
	merge 1:1 user_id day using other_push_count
	replace other_push_count = 0 if _merge == 1 // if not matched, store as 0
	drop if _merge == 2 // drop rows only from using data_name
	drop _merge
	rename other_push_count other_push_count_td
	di "Completed merge with other_push_count."
	
	* Gen screenon_total
	di "Starting merge with screenon_total..."
	merge m:1 user_id using screenon_total
	drop if _merge == 2
	drop _merge
	di "Completed merge with screenon_total."
	
	* Output
	save "treat_df_`gap'.dta", replace
end


* Call Functions
gen_merge_df 60
// gen_total_screenon
gen_treat_df 60

di ">>> ENDING DATA MERGE <<<"
