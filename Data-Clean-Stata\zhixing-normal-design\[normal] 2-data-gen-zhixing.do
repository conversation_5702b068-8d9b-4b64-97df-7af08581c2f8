***************************************************
* SCREEN-ON PUSH ANALYSIS I: ZHIXING
* This file: Do-file for data merging
* Author: Xuhang Fan
* File Structure:
*   - 01-clean-zx.do: Data import and cleaning
*   - 02-merge-zx.do: Merging datasets
*   - 03-main-rd-zx.do: Main regression discontinuity analysis
*   - 04-extra-rd-zx.do: Additional regression discontinuity analysis
*   - output/: Directory for results and logs
***************************************************
*!version: 2025-05-25, STATA 18
***************************************************
* ssc install winsor2
* ssc install gtools
* ssc install joinby
***************************************************

di ">>> RUNNING DATA MERGE <<<"

****************
* 0. Settings  *
****************
clear all
log using "02-merge-zx.log", replace
set seed 123
cd "I:\Dropbox\Research\Screen-on Push\Raw Data\Zhixing\0402_0506"
* set processors 32
* set max_memory 128g

***************************
* 1. Generate: merged_df  *
***************************

* Function: gen_merge_df
* Purpose: Merge push notification data with screen-on data and filter by time gap
* Arguments:
    * gap - Time window in minutes around push notification request time
* Output: merged_df.dta containing joined push and screen-on data within specified time window
capture program drop gen_merge_df
program define gen_merge_df
	args gap
    * Joinby
    di "=== Start Joinby ==="
		use push_df, clear
		joinby user_id day using screenon_df
    di "=== End Joinby ==="
    * Sort and Re-order
	sort task_id user_id screenon_time
	order task_id user_id request_time screenon_time sent_time ddl_time arrival_time day
	* Add Time relative to Requests
	gen screenon_t = (screenon_time - request_time) / 60000
    gen sent_t = (sent_time - request_time) / 60000
	* Drop Data
	keep if abs(screenon_t) <= `gap'
	* Store
	save merged_df, replace
end

* Function: gen_total_screenon
* Purpose: Calculate total screen-on count per user across all days
* Arguments: None (operates on screenon_df.dta)
* Output: screenon_total.dta containing user_id and total screen-on count
capture program drop gen_total_screenon
program define gen_total_screenon
	use screenon_df, clear
	gduplicates tag user_id, gen(screenon_total)
	replace screenon_total = screenon_total + 1
	gduplicates drop user_id, force
	keep user_id screenon_total
	save screenon_total, replace
end

* Function: gen_treat_df
* Purpose: Generate final treatment dataset with user characteristics, login counts, and treatment indicators
* Arguments:
    * gap - Time window in minutes (used for output file naming)
* Output: treat_df_[gap].dta containing complete analysis dataset with all merged variables
capture program drop gen_treat_df
program define gen_treat_df
	args gap
	* Load data
	use merged_df, clear

	* Filter Data
	* Keep the signal closest to each request
	gen double abs_t = abs(screenon_t)
	bysort task_id (abs_t): keep if _n == 1
	drop abs_t

	* Treatment Indicator
	gen treated = (sent_time <= ddl_time)

	* Generate screen_on_near
	gen screenon_int = int(screenon_t)

	* Merge usertags
	di "=== Starting merging usertags ==="
	fmerge m:1 user_id using usertags
	drop if _merge == 2
	drop _merge
	di "=== Completed merging usertags ==="

	* Gen big_phone_brand: most popular Android phone brands
	gen big_phone_brand = inlist(phone_brand, "华为", "小米", "OPPO", "Vivo")

	* Gen lc0: app login count on the day of push
	di "=== Start merge with login_count ==="
	merge 1:1 user_id day using login_count
	replace login_count = 0 if _merge == 1 // if not matched, store as 0
	drop if _merge == 2 // drop rows only from using data_name
	drop _merge
	rename login_count login_count_td
	di "=== Completed merge with login_count ==="

	* Process login count
	* 1. login_count_td_w with [1, 99] winsor
	winsor2 login_count_td

	* Gen other_push_count_td
	di "Starting merge with other_push_count..."
	merge 1:1 user_id day using other_push_count
	replace other_push_count = 0 if _merge == 1 // if not matched, store as 0
	drop if _merge == 2 // drop rows only from using data_name
	drop _merge
	rename other_push_count other_push_count_td
	di "Completed merge with other_push_count."

	* Gen screenon_total
	di "Starting merge with screenon_total..."
	merge m:1 user_id using screenon_total
	drop if _merge == 2
	drop _merge
	di "Completed merge with screenon_total."

	* Output
	save "treat_df_`gap'.dta", replace
end


* Call Functions
gen_merge_df 60
// gen_total_screenon
gen_treat_df 60

di ">>> ENDING DATA MERGE <<<"
