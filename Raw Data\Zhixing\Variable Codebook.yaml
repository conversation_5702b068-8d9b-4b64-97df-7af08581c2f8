# Variable Codebook for a full screen-on push policy data from Zhixing app, Daily Push Inc. 
# The data does not have variable, which is listed in this yaml
# The data has keys as file names and values as its variable lists.
# Example
# file.txt:
#   var1
#   var2
#   var3

zhixing_usertags.txt:
  - user_id
  - age
  - gender
  - is_parent
  - spending_level
  - job
  - recent_behavior
  - province
  - city	
  - phone_brand
  - phone_lang	
  - phone_carrier	
  - phone_model	
  - interest_tags	

zhixing_cid_logincnt_0418_0425.txt:
  day
  user_id
  login_count

zhixing_else_push_times_0418_0425_1.txt:
  day
  user_id
  other_push_count

zhixing_liangping_time_0418_0425.txt:
  user_id
  screenon_time
  screenon_appid
  screenon_appname

zhixing_liangping_push_time_0418_0425.txt:
  app_id
  task_id
  user_id
  ddl_time
  request_time
  sent_time
  arrival_time
  click_time
  sent_success



