capture {
clear
set processors 1
cd "D:\Dropbox\Research\Screen-on Push\Raw Data\zju\0402_0506/"
sysdir set PERSONAL "C:\Users\<USER>\ado\personal/"
global S_ADO = `"BASE;SITE;.;PERSONAL;PLUS;OLDPLACE"'
mata: mata mlib index
mata: mata set matalibs "lmatabase;lmataado;lmatabma;lmatacollect;lmataerm;lmatafc;lmatagsem;lmatahetdid;lmataivqreg;lmatajm;lmatalasso;lmatamcmc;lmatameta;lmatami;lmatamixlog;lmatanumlib;lmataopt;lmatapath;lmatapostest;lmatapss;lmatasem;lmatasp;lmatasvy;lmatatab;libjson;lparallel;lxtabond2"
set seed 59214
noi di "{hline 80}"
noi di "Parallel computing with stata (by GVY)"
noi di "{hline 80}"
noi di `"cmd/dofile   : "foreach nbins in 30 50 100 {""'
noi di "pll_id       : ar38m74y13"
noi di "pll_instance : 3/4"
noi di "tmpdir       : `c(tmpdir)'"
noi di "date-time    : `c(current_time)' `c(current_date)'"
noi di "seed         : `c(seed)'"
noi di "{hline 80}"
local pll_instance 3
local pll_id ar38m74y13
global pll_instance 3
global pll_id ar38m74y13
set maxvar 5000
set matsize 400
}
local result = _rc
if (c(rc)) {
cd "D:\Dropbox\Research\Screen-on Push\Raw Data\zju\0402_0506/"
mata: parallel_write_diagnosis(strofreal(c("rc")),"D:\Dropbox\Research\Screen-on Push\Raw Data\zju\0402_0506/__pllar38m74y13_finito0003","while setting memory")
clear
exit
}

/* Checking for break */
mata: parallel_break()

/* Loading Globals */
capture {
cap run "D:\Dropbox\Research\Screen-on Push\Raw Data\zju\0402_0506/__pllar38m74y13_glob.do"
}
if (c(rc)) {
  cd "D:\Dropbox\Research\Screen-on Push\Raw Data\zju\0402_0506/"
  mata: parallel_write_diagnosis(strofreal(c("rc")),"D:\Dropbox\Research\Screen-on Push\Raw Data\zju\0402_0506/__pllar38m74y13_finito0003","while loading globals")
  clear
  exit
}

/* Checking for break */
mata: parallel_break()
capture {
  noisily {
    use "D:\Dropbox\Research\Screen-on Push\Raw Data\zju\0402_0506/__pllar38m74y13_dataset" if _ar38m74y13cut == 3

/* Checking for break */
mata: parallel_break()
    foreach nbins in 30 50 100 {
  }
}
if (c(rc)) {
  cd "D:\Dropbox\Research\Screen-on Push\Raw Data\zju\0402_0506/"
  mata: parallel_write_diagnosis(strofreal(c("rc")),"D:\Dropbox\Research\Screen-on Push\Raw Data\zju\0402_0506/__pllar38m74y13_finito0003","while running the command/dofile")
  clear
  exit
}
mata: parallel_write_diagnosis(strofreal(c("rc")),"D:\Dropbox\Research\Screen-on Push\Raw Data\zju\0402_0506/__pllar38m74y13_finito0003","while executing the command")
save "D:\Dropbox\Research\Screen-on Push\Raw Data\zju\0402_0506/__pllar38m74y13_dta0003", replace
cd "D:\Dropbox\Research\Screen-on Push\Raw Data\zju\0402_0506/"
