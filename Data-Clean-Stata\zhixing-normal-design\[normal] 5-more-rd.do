* This file do:
* 1. rd using lag Y, define lag

**********************
* Parameters Setting *
**********************

* Define cutoff
local c 0
* Define time window around cutoff (in minutes)
local gap 15 
* Define how many periods as lagging Y
local lag 3    


***************
* Preparation *
***************

* Clear memory and set working directory
clear all
cd "I:\Dropbox\Research\Screen-on Push\Raw Data\zju\0402_0506"

* Load data
use treat_df_60, replace
keep if screenon_t <= `gap' & screenon_t >= -`gap'

* Create output directory if it doesn't exist
cap mkdir output
cap mkdir "output/rdrobust"
cap mkdir "output/rdplot"

* Rename login count today
rename login_count_td_w lc0
label variable lc0 "lc0"

******************
* Lagging Effect *
******************

* Day var
rename day day_origin
egen day_max = max(day_origin)
egen day_min = min(day_origin)
format day_max %td
format day_min %td

* Loop to generate variables and build day vars
forval l = 1/`lag' {
    * Check if day`l' exists
    capture confirm variable day`l'
    if !_rc {
        drop day`l'
        di "=== Day`l' existed and was dropped ==="
    }
    gen day`l' = day_origin + `l'
    format day`l' %td
    di "=== Day`l' Generated ==="
	
    * Merge with login_count data
    di "=== Start merge: day`l' ==="
    rename day`l' day
    merge m:1 user_id day using login_count, keep(master match)
    rename day day`l'
	rename login_count lc`l'
	di "=== End merge: day`l' ==="
	
	* Add 0 to unmatched but in-range data
	replace lc`l' = 0 if _merge == 1 & day`l' <=  day_max & day`l' >= day_min
	drop _merge
}

* Winsorizing Y
forval l = 1/`lag' {
	winsor2 lc`l', replace cuts(1 99) trim
	di "=== lc`l' winsorized (replaced at 1%, 99%) ==="
}

* RD Plots and Export
* p = 2
forval l = 0/`lag' {
	forval bw = 3(3)9 {
			preserve
			keep if screenon_t <= `bw' & screenon_t >= -`bw'
			qui rdplot lc`l' screenon_t, c(0) p(2) h(`bw') binselect(es) ci(95) graph_options(title("RD Plot: App 1 Data", size(medium)) ytitle("App Login Count at Day T+`l'") xtitle("Screen-on Time in [-`bw', +`bw']") graphregion(color(white)))
			gr export "output/rdplot/rdp_lc`l'_p`p'_bw`bw'.pdf", replace
			restore
	}
}

* RD: run rdrobust
forval l = 0/`lag' {
	forval p = 1/2  {
		forval bw = 3(3)12 {
			eststo rd_lc`l'_p`p'_bw`bw'_c`c': rdrobust lc`l' screenon_t, fuzzy(treated) c(0) p(`p') h(`bw') b(`bw') masspoints(adjust) all
		}
	}
}

* RD: Export tables
forval l = 0/`lag' {
	forval p = 1/2  {
		esttab rd_lc`l'_p`p'_bw3_c0 rd_lc`l'_p`p'_bw6_c0 rd_lc`l'_p`p'_bw9_c0 rd_lc`l'_p`p'_bw12_c0 using "output/rdrobust/rd-lc`l'-p`p'.tex", replace se star(* 0.10 ** 0.05 *** 0.01) cells(b(star fmt(3)) se(par fmt(3))) stats(N_b_l pv_rb b_l p, labels("Effect. Obs. (Left)" "p-value (robust)" "Bandwidth (left)" "Order") fmt(0 3 0)) mlabels(none) collabels(none) title("RD Estimates for App Login in Day T+`l' (Polynomial Order `p')") label booktabs
		* note("Notes: Estimates from polynomial order `p'. Coefficients are adjusted if mass points detected. Standard errors in parentheses. Significance levels are: * p < 0.10, ** p < 0.05, *** p < 0.01.")
	}
}

***************
* Time effect *
***************

* Weekend effect
gen weekend = 0
replace weekend = 1 if inlist(day_origin, td(06apr2024), td(07apr2024), td(13apr2024), td(14apr2024), td(20apr2024), td(21apr2024), td(27apr2024), td(28apr2024), td(04may2024), td(05may2024))

forval l = 0/`lag' {
	forval p = 1/2  {
		forval bw = 3(3)12 {
			eststo rd_weekend_lc`l'_p`p'_bw`bw'_c`c': rdrobust lc`l' screenon_t if weekend == 1, fuzzy(treated) c(0) p(`p') h(`bw') b(`bw') masspoints(adjust) all
			eststo rd_weekday_lc`l'_p`p'_bw`bw'_c`c': rdrobust lc`l' screenon_t if weekend == 0, fuzzy(treated) c(0) p(`p') h(`bw') b(`bw') masspoints(adjust) all		
		}
	}
}

forval p = 1/2 {
	forval bw = 3(3)12 {
		esttab rd_weekend_lc0_p`p'_bw`bw'_c`c' rd_weekend_lc1_p`p'_bw`bw'_c`c' rd_weekend_lc2_p`p'_bw`bw'_c`c' rd_weekend_lc3_p`p'_bw`bw'_c`c' using "output/rdrobust/rd-weekend-p`p'-bw`bw'.tex", replace se star(* 0.10 ** 0.05 *** 0.01) stats(N_b_l pv_rb b_l p, labels("Effect. Obs. (Left)" "p-value (robust)" "Bandwidth (left)" "Order") fmt(0 3 0)) mlabels(none) collabels(none) title("RD Estimates: weekend (bw = `bw', p = `p')") label booktabs
		esttab rd_weekday_lc0_p`p'_bw`bw'_c`c' rd_weekday_lc1_p`p'_bw`bw'_c`c' rd_weekday_lc2_p`p'_bw`bw'_c`c' rd_weekday_lc3_p`p'_bw`bw'_c`c' using "output/rdrobust/rd-weekday-p`p'-bw`bw'.tex", replace se star(* 0.10 ** 0.05 *** 0.01) stats(N_b_l pv_rb b_l p, labels("Effect. Obs. (Left)" "p-value (robust)" "Bandwidth (left)" "Order") fmt(0 3 0)) mlabels(none) collabels(none) title("RD Estimates: weekday (bw = `bw', p = `p')") label booktabs
	}
}


* Working hour effect

* Convert sent time to hours (0-24)
gen daytime = (hh(sent_time) + mm(sent_time)/60 + ss(sent_time)/3600)

* Loop over 2-hour intervals from 9 to 20
forval t = 9(3)18 {
	di "RD in (`t', `t'+3)"
	count if daytime >= `t' & daytime < `t' + 3
    eststo rd_hour_lc0_p1_bw6_c0_t`t': rdrobust lc0 screenon_t if daytime >= `t' & daytime <= `t' + 3, fuzzy(treated) c(0) p(1) h(6) b(6) masspoints(adjust) all	
}
  v；；【怕。
* Output: 
esttab rd_hour_* using "output/rdrobust/rd-hour-lc0-p1-bw6.tex", replace se star(* 0.10 ** 0.05 *** 0.01) stats(N_b_l pv_rb b_l p, labels("Effect. Obs. (Left)" "p-value (robust)" "Bandwidth (left)" "Order") fmt(0 3 0)) mlabels(none) collabels(none) title("RD Estimates: hours effect (every 3 h, bw = `b', p = `p')") label booktabs

*******************
* App type Effect *
*******************

* Work-related app
preserve  
contract screenon_appname screenon_appid, p(percent) nomiss
gsort -percent
list screenon_appname screenon_appid percent in 1/50, noobs clean
keep in 1/50
texsave screenon_appname percent using "output/top_apps.tex", replace title("Top 50 Apps by Screen-on Time Percentage") 
restore

* Define local macro with workapps list
local workapps "JpUFLntDPy991OP8ISVlQ3 MUOSRCfwmdAX8LfhksYEA soAgXpVfCaAdens98kjkh dTImhLGEEW9Xy2NoxYWc7 NjRiP2tvdz6wc1sebUxIE2 Cte3dZnvRC8QnUSNl7WZv5 iN6FrJBWaD87w3HVmPXZ24 8san1asHKo84V0I1GSHFW7"

* Generate work variable
gen work = 0  // Initialize work as 0
foreach app in `workapps' {
    replace work = 1 if screenon_appid == "`app'"  // Set work=1 if appid matches
}

rdrobust login_count_td_w screenon_t if work == 0, fuzzy(treated) c(0) p(1) masspoints(adjust) all		