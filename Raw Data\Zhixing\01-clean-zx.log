-------------------------------------------------------------------------------------------------------------------------------------------------------------------
      name:  <unnamed>
       log:  I:\Dropbox\Research\Screen-on Push\Raw Data\Zhixing\01-clean-zx.log
  log type:  text
 opened on:  26 May 2025, 02:35:52

. include "I:\Dropbox\Research\Screen-on Push\Data-Clean-Stata\recode.do"

. // Drop non-unique user_id
. duplicates drop user_id, force
variable user_id not found
r(111);
r(111);

. include "I:\Dropbox\Research\Screen-on Push\Data-Clean-Stata\recode.do"

. capture program drop recode

. program define recode
  1.     // Drop non-unique user_id
.     duplicates drop user_id, force
  2.     // Recode gender
.     gen gender_recoded = .
  3.     replace gender_recoded = 1 if gender == "男"
  4.     replace gender_recoded = 0 if gender == "女"
  5.     // Recode spending_level
.     gen spending_level_recoded = .
  6.     replace spending_level_recoded = 1 if spending_level == "消费水平(低)"
  7.     replace spending_level_recoded = 2 if spending_level == "消费水平(中)"
  8.     replace spending_level_recoded = 3 if spending_level == "消费水平(高)"
  9.     // Recode is_parent
.     gen is_parent_recoded = .
 10.     replace is_parent_recoded = 1 if is_parent == "父母"
 11.     // Recode job
.     gen job_recoded = .
 12.     replace job_recoded = 1 if job == "白领"
 13.     replace job_recoded = 0 if job == "蓝领"
 14.     // Recode age
.     gen age_recoded = .
 15.     replace age_recoded = 1 if age == "18-24"
 16.     replace age_recoded = 2 if age == "25-34"
 17.     replace age_recoded = 3 if age == "35-44"
 18.     replace age_recoded = 4 if age == "45+"
 19.     // Recode recent_behavior
.     gen recent_behavior_recoded = .
 20.     replace recent_behavior_recoded = 1 if recent_behavior == "搬家"
 21.     replace recent_behavior_recoded = 0 if recent_behavior == "国内旅游"
 22.     // Generate east_province, = 1 if province is in east
.     gen east_province = .
 23.     * Assign 1 to provinces in the east
.     replace east_province = 1 if inlist(province, "北京市", "天津市", "河北省", "山西省", "辽宁省", "吉林省", "黑龙江省", "上海市")
 24.     replace east_province = 1 if inlist(province, "江苏省", "浙江省", "安徽省", "福建省", "山东省", "河南省", "湖北省", "湖南省")
 25.     replace east_province = 1 if inlist(province, "广东省", "海南省", "重庆市", "四川省", "中国台湾", "中国香港", "中国澳门")
 26.     * Assign 0 to provinces not in the east
.     replace east_province = 0 if inlist(province, "内蒙古自治区", "广西壮族自治区", "西藏自治区", "宁夏回族自治区", "新疆维吾尔自治区", "青海省", "陕西省")
 27.     replace east_province = 0 if inlist(province, "甘肃省", "贵州省", "云南省")
 28.     // Recode phone_model
.     gen phone_model_recoded = .
 29.     replace phone_model_recoded = 10 if phone_model == "android10"
 30.     replace phone_model_recoded = 11 if phone_model == "android11"
 31.     replace phone_model_recoded = 12 if phone_model == "android12"
 32.     replace phone_model_recoded = 13 if phone_model == "android13"
 33.     replace phone_model_recoded = 3 if phone_model == "android3及以下版本"
 34.     replace phone_model_recoded = 4 if phone_model == "android4"
 35.     replace phone_model_recoded = 6 if phone_model == "android6"
 36.     replace phone_model_recoded = 7 if phone_model == "android7"
 37.     replace phone_model_recoded = 8 if phone_model == "android8"
 38.     replace phone_model_recoded = 9 if phone_model == "android9"
 39.     // Recode interest_tags
.     gen interest_tags_recoded = .
 40.     replace interest_tags_recoded = 1 if interest_tags == "旅游出行-低"
 41.     replace interest_tags_recoded = 2 if interest_tags == "旅游出行-中"
 42.     replace interest_tags_recoded = 3 if interest_tags == "旅游出行-高"
 43.     // Get a list of all variables
.     ds
 44.     local all_vars `r(varlist)'
 45.     // Loop through each variable
.     foreach var of local all_vars {
 46.         // Check if the variable name ends with _recoded
.         if substr("`var'", -8, 8) == "_recoded" {
 47.             // Get the original variable name
.             local original_var = substr("`var'", 1, strlen("`var'") - 8)
 48.             // Drop the original variable
.             drop `original_var'
 49.             // Rename the recoded variable to the original name
.             rename `var' `original_var'
 50.         }
 51.     }
 52. end

. . help merge

. help merge

. help merge

. help winsor2

. exit, clear
