-------------------------------------------------------------------------------------------------------------------------------------------------------------------
      name:  <unnamed>
       log:  I:\Dropbox\Research\Screen-on Push\Raw Data\Zhixing\01-clean-zx--02-27-00.log
  log type:  text
 opened on:  26 May 2025, 02:27:00

. cd "I:\Dropbox\Research\Screen-on Push\Raw Data\Zhixing"
I:\Dropbox\Research\Screen-on Push\Raw Data\Zhixing

. 
. local timestamp: di %tc CCYY-MM-DD "-" %tcHH-MM-SS `=clock("`c(current_date)' `c(current_time)'", "DMY hms")'
CCYY not found
r(111);

. 
. // Start log file
/ is not a valid command name
r(199);

. 
. log using "01-clean-zx-`timestamp'.log", replace
log file already open
r(604);

. clear all

. 
. set seed 123

. 
. cd "I:\Dropbox\Research\Screen-on Push\Raw Data\Zhixing"
I:\Dropbox\Research\Screen-on Push\Raw Data\Zhixing

. 
. local timestamp: di %tc CCYY-MM-DD "-" %tcHH-MM-SS `=clock("`c(current_date)' `c(current_time)'", "DMY hms")'
CCYY not found
r(111);

. 
. log using "01-clean-zx-`timestamp'.log", replace
log file already open
r(604);

. local timestamp: di %tc CCYY-MM-DD "-" %tcHH-MM `=clock("`c(current_date)' `c(current_time)'", "DMY hms")'
CCYY not found
r(111);

. 
. log using "01-clean-zx-`timestamp'.log", append
log file already open
r(604);

. cd "I:\Dropbox\Research\Screen-on Push\Raw Data\Zhixing"
I:\Dropbox\Research\Screen-on Push\Raw Data\Zhixing

. 
. local timestamp: di %tc CCYY-MM-DD "-" %tc HH-MM `=clock("`c(current_date)' `c(current_time)'", "DMY hms")'
CCYY not found
r(111);

. 
. log using "01-clean-zx-`timestamp'.log", append
log file already open
r(604);

. clear all

. 
. set seed 123

. 
. cd "I:\Dropbox\Research\Screen-on Push\Raw Data\Zhixing"
I:\Dropbox\Research\Screen-on Push\Raw Data\Zhixing

. 
. local timestamp: di %tdCCYY-MM-DD "_" %tcHH-MM-SS `=clock("`c(current_date)' `c(current_time)'", "DMY hms")'

. 
. log using "output/01-clean-zx-`timestamp'.log", append
log file already open
r(604);

. di %tdCCYY-MM-DD "_" %tcHH-MM-SS `=clock("`c(current_date)' `c(current_time)'", "DMY hms")'
_02-32-54

. log close _all
      name:  <unnamed>
       log:  I:\Dropbox\Research\Screen-on Push\Raw Data\Zhixing\01-clean-zx--02-27-00.log
  log type:  text
 closed on:  26 May 2025, 02:33:46
-------------------------------------------------------------------------------------------------------------------------------------------------------------------
